import React, { ReactElement } from "react";
import * as FeatherIcon from 'react-feather';
import { app, bs, grid, entity, util, input, chart } from "@datatp-ui/lib";

import { T } from "../../Dependency";

const SESSION = app.host.DATATP_SESSION;


class UIReportList extends entity.DbEntityList {
  createVGridConfig() {
    let config: grid.VGridConfig = {
      record: {
        control: {
          width: 20, items: []
        },
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          let rec = dRec.record;
          if (rec.rowHeight > 0) return rec.rowHeight;
          return 150;
        },
        onchangeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord, deltaY: number) => {
          let rec = dRec.record;
          let currHeight = rec.rowHeight;
          if (currHeight < 10) currHeight = grid.DEFAULT.row.height;
          rec.rowHeight = currHeight + deltaY;
          return true;
        },
        fields: [
          {
            label: T("NO."), name: "stt", width: 30, container: 'fixed-left',
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T('SALESMAN'), name: 'saleman', width: 100, container: 'fixed-left',
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          { label: T('NO. OF INQUIRY'), name: 'inquiry-count', width: 160 },
          { label: T("NO. OF WON SHIPMENT"), name: 'shipment-count', width: 210 },
          { label: T("% WIN"), name: 'win-rate', width: 80 },
          {
            label: T("AGENT (WON)"), name: 'agent-won', width: 400,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T("COUNTRY"), name: 'country', width: 150,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T("NETWORK"), name: 'network', width: 120,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T("SOURCE"), name: 'source', width: 150,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T("SERVICE"), name: 'service', width: 400,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
        ],
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_EXPORT_XLSX()
        ]
      },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }
}

interface UIReportIndividualQuotationProps extends app.AppComponentProps {
  reportRecords: Array<any>
  records: Array<any>
}

export class UIReportIndividualQuotation extends app.AppComponent<UIReportIndividualQuotationProps> {
  collectVolume = (rec: any) => {
    let volume = 'N/A';
    if (rec['exp-20'] && rec['exp-20'] !== 0) volume = rec['exp-20'] + "x20'";
    if (rec['exp-40'] && rec['exp-40'] !== 0) volume = rec['exp-40'] + "x40'";
    if (rec['exp-45'] && rec['exp-45'] !== 0) volume = rec['exp-45'] + "x45'";
    if (rec['exp-lcl-cbm'] && rec['exp-lcl-cbm'] !== 0) volume = rec['exp-lcl-cbm'] + " (CBM)";
    if (rec['exp-air-kgs'] && rec['exp-air-kgs'] !== 0) volume = rec['exp-air-kgs'] + " (KGS)'";
    if (rec['exp-breakbulk-shmt'] && rec['exp-breakbulk-shmt'] !== 0) volume = rec['exp-breakbulk-shmt'] + " (Shpt)";
    if (rec['imp-20'] && rec['imp-20'] !== 0) volume = rec['imp-20'] + "x20'";
    if (rec['imp-40'] && rec['imp-40'] !== 0) volume = rec['imp-40'] + "x40'";
    if (rec['imp-45'] && rec['imp-45'] !== 0) volume = rec['imp-45'] + "x45'";
    if (rec['imp-lcl-cbm'] && rec['imp-lcl-cbm'] !== 0) volume = rec['imp-lcl-cbm'] + " (CBM)";
    if (rec['imp-air-kgs'] && rec['imp-air-kgs'] !== 0) volume = rec['imp-air-kgs'] + " (KGS)'";
    if (rec['breakbulk-shpt-i'] && rec['breakbulk-shpt-i'] !== 0) volume = rec['breakbulk-shpt-i'] + " (Shpt)";
    return volume;
  }

  render() {
    let { appContext, pageContext, records, reportRecords } = this.props;
    let recordMapBySaleman: Map<string, Array<any>> = new Map<string, Array<any>>();

    for (let record of reportRecords) {
      let saleman = record['_owner_'];
      if (!saleman || saleman.length < 1) continue;

      if (!recordMapBySaleman.get(saleman)) {
        recordMapBySaleman.set(saleman, [record]);
      } else {
        let recs = recordMapBySaleman.get(saleman)!;
        recs.push(record);
        recordMapBySaleman.set(saleman, recs);
      }
    }

    recordMapBySaleman = new Map([...recordMapBySaleman.entries()].sort())

    let reportByMonthRecords: any = [];
    let stt = 1;

    for (let saleman of recordMapBySaleman.keys()) {
      let inquiryCount = 0;
      let shipmentCount = 0;
      let winRate = 0;
      let agentWon = '';
      let country = '';
      let networks = '';
      let sources = '';
      let service = '';
      let recs: any = recordMapBySaleman.get(saleman);

      for (let rec of recs) {
        if (!rec['booking-qty'] || rec['booking-qty'] == 0) continue;
        shipmentCount += rec['booking-qty'] ? rec['booking-qty'] : 0;
        agentWon += rec['agent-name'] ? "- " + rec['agent-name'] + '\r\n' : '- N/A \r\n';
        country += rec['country'] ? "- " + rec['country'] + '\r\n' : '- N/A \r\n';
        networks += rec['network'] ? "- " + rec['network'] + '\r\n' : '- N/A \r\n';
        sources += rec['source'] ? "- " + rec['source'] + '\r\n' : '- N/A \r\n';
        let volume = this.collectVolume(rec);
        let incoterms = rec['incoterms'] ? rec['incoterms'] : 'N/A \r\n';
        let routes = rec['route'] ? rec['route'] : 'N/A \r\n';
        service += "- " + volume + " & " + incoterms + " & " + routes + '\r\n';
      }

      inquiryCount = recs.length;
      winRate = (shipmentCount / inquiryCount);

      reportByMonthRecords.push({
        'rowHeight': 150,
        'stt': stt++,
        'saleman': saleman,
        'inquiry-count': inquiryCount,
        'shipment-count': shipmentCount,
        'win-rate': util.text.formater.percent(winRate),
        'agent-won': agentWon.length > 0 ? agentWon : '- N/A',
        'country': country.length > 0 ? country : '- N/A',
        'network': networks.length > 0 ? networks : '- N/A',
        'source': sources.length > 0 ? sources : '- N/A',
        'service': service.length > 0 ? service : '- N/A',
      })
    }
    return (
      <div className="flex-vbox">
        <UIReportList key={`report-by-month`} appContext={appContext} pageContext={pageContext}
          plugin={new entity.DbEntityListPlugin(reportByMonthRecords)} style={{ minHeight: 600 }} />
      </div>
    )
  }
}