import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, bs } from '@datatp-ui/lib';
import { BBRefCountry } from 'module/settings';
import { UIRowList } from '../data/UIRowList';
import { T } from '../Dependency';

export const initVgridConfigBDQuotationConfigPlugin = (context: grid.VGridContext) => {

  let uiRoot = context.uiRoot as UIRowList;
  uiRoot.onAddRecord = (record: any, callback: (record: any) => void) => {
    record.status = 'Checking';
    callback(record);
  };

  let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
    let field = fieldCtx.fieldConfig;
    let dRecord = fieldCtx.displayRecord;
    let ctx = fieldCtx.gridContext;
    let event: grid.VGridCellEvent = {
      row: dRecord.row,
      field: field,
      event: 'Modified'
    }
    ctx.broadcastCellEvent(event);
  }

  for (let field of context.config.record.fields) {
    if (!['_selector_', 'agent-name', 'country', 'subject-mail', 'reasonnote'].includes(field.name)) {
      field.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
        return 'text-center';
      }
    }

    if (field.name === 'country') {
      field.editor = {
        type: 'string',
        renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
          let record = ctx.displayRecord.record;
          let uiRoot = context.uiRoot as UIRowList;
          let { appContext, pageContext } = uiRoot.props;
          return (<BBRefCountry
            appContext={appContext} pageContext={pageContext}
            placement="bottom-start" offset={[0, 5]} minWidth={300}
            placeholder="Enter Country" required bean={record} beanIdField={''} beanLabelField={'country'}
            refCountryBy={'id'} allowUserInput={true}
            onPostUpdate={(inputUI, bean, selOpt, userInput) => {
              record['country'] = selOpt.label;
              onInputChange(record, 'country', '', selOpt.label);
            }}
          />)
        },
        onInputChange: onInputChange
      }
    }

    let renderStatusOpt = (status: string, color: string, StatusIcon: any, onSelectStatus: (status: string) => void) => {
      return (
        <div key={'opt-' + status}
          className={`d-flex flex-center px-2 py-1 rounded-2 bg-${color}-subtle text-${color} w-100 cursor-pointer fw-bold`}
          onClick={() => onSelectStatus(status)
          }>
          <StatusIcon size={12} className="me-1" />
          <span>{status}</span>
        </div>
      )
    }

    if (field.name == 'status') {
      if (field.editor) field.editor.renderCustom = (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
        let uiRoot = ctx.gridContext.uiRoot as UIRowList;
        let record = ctx.displayRecord.record;
        let status = record['status'] || 'N/A';
        let color = 'info';
        let StatusIcon = FeatherIcon.X;
        if (status == 'Checking') {
          color = 'danger';
          StatusIcon = FeatherIcon.Loader;
        } else if (status == 'Quoted') {
          color = 'warning';
          StatusIcon = FeatherIcon.MessageCircle;
        } else if (status == 'Followed Up') {
          color = 'primary';
          StatusIcon = FeatherIcon.ThumbsUp;
        } else if (status == 'Won') {
          color = 'success';
          StatusIcon = FeatherIcon.CheckCircle;
        } else if (status == 'Lost') {
          color = 'secondary';
          StatusIcon = FeatherIcon.XCircle;
        }

        let onSelectStatus = (status: string) => {
          record['status'] = status;
          onInputChange(record, 'status', '', status);
          uiRoot.forceUpdate();
        }
        return (
          <div className={`flex-hbox w-100 ${color} fw-bold`}>
            <bs.Popover className="d-flex flex-center w-100 h-100"
              title={T('Status')} closeOnTrigger=".btn" >
              <bs.PopoverToggle
                className={`flex-hbox align-items-center flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                <StatusIcon size={12} className="me-1" />
                <span>{status}</span>
              </bs.PopoverToggle>
              <bs.PopoverContent>
                <div className='flex-vbox gap-2' style={{ width: '130px' }}>
                  {renderStatusOpt('Checking', 'danger', FeatherIcon.Loader, onSelectStatus)}
                  {renderStatusOpt('Quoted', 'warning', FeatherIcon.MessageCircle, onSelectStatus)}
                  {renderStatusOpt('Followed Up', 'primary', FeatherIcon.ThumbsUp, onSelectStatus)}
                  {renderStatusOpt('Won', 'success', FeatherIcon.CheckCircle, onSelectStatus)}
                  {renderStatusOpt('Lost', 'secondary', FeatherIcon.XCircle, onSelectStatus)}
                </div>
              </bs.PopoverContent>
            </bs.Popover>
          </div>
        );
      }
    }
  }
}
