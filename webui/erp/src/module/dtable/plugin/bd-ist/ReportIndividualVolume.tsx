import React, { ReactElement } from "react";
import * as FeatherIcon from 'react-feather';
import { app, bs, grid, entity, util, input, chart } from "@datatp-ui/lib";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ReferenceLine, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';


import { T } from "../../Dependency";
import { UIRowList } from "../../data/UIRowList";

const SESSION = app.host.DATATP_SESSION;


class PieChartTemplateModel extends chart.PieChartTemplateModel<any> {
  label: string = 'VOLUME';
  constructor(config: chart.PieChartTemplateConfig, records: Array<any>) {
    super(config, records);
  }

  override renderLegendContent = (pieProps: any): ReactElement => {
    const { payload } = pieProps;
    let total: number = 0;
    let html = (
      <div style={{ fontSize: 10, overflow: "scroll", maxHeight: 350, width: 400 }}>
        <table className='table table-sm table-hover'>
          <thead>
            <tr>
              <th scope="col">SALEMAN</th>
              <th scope="col">{this.label}</th>
              <th scope="col">PERCENT</th>
            </tr>
          </thead>
          <tbody>
            {payload.map((entry: any, index: any) => {
              total += entry.payload.value
              return <tr style={{ color: entry.color }}>
                <td scope="row" className='flex-hbox'>
                  <div className="fw-bold" > {entry.payload.name} </div>
                </td>
                <td scope="row" className='text-right'> {entry.payload.originalValue !== undefined ? entry.payload.originalValue : entry.payload.value} </td>
                <td scope="row"> {`${entry.payload.percent ? (entry.payload.percent * 100).toFixed(0) : 0}%`} </td>
              </tr>
            })}
            <tr>
              <td scope="row" className='flex-hbox'>
                <div className="fw-bold text-uppercase" >TOTAL</div>
              </td>
              <td scope="row" className='text-right'>{total}</td>
              <td scope="row"></td>
            </tr>
          </tbody>
        </table>
      </div>
    )
    return html;
  }

  override computeData() {
    let recordMap: Map<string, number> = new Map<string, number>();
    for (let record of this.records) {
      if (!recordMap.has(record['_owner_'])) {
        recordMap.set(record['_owner_'], 0);
      } else {
        let total = recordMap.get(record['_owner_'])!;
        let volume = this.calculateVolume(record);
        total += volume
        recordMap.set(record['_owner_'], total);
      }
    }
    let records = Array.from(recordMap, ([key, value]) => {
      // Ensure minimum value for display when value is 0
      const displayValue = value === 0 ? 0.1 : value;
      return { 'name': key.toUpperCase(), 'value': displayValue, 'originalValue': value };
    });
    return records;
  }

  calculateVolume = (rec: any) => {
    return 0;
  }

  override formatTooltip(value: any) {
    return value;
  }

  override renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }: any): any => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);
    return (
      <text
        x={x} y={y} textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central"
        className='fw-bold' fill='white' style={{ fontSize: 10 }}>
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };
}

class TEUPieChart extends PieChartTemplateModel {
  label = 'TEU';

  calculateVolume = (rec: any) => {
    let volume = 0;
    if (rec['status'] !== 'Won') return volume;
    if (rec['exp-20'] && rec['exp-20'] !== 0) volume += rec['exp-20'];
    if (rec['exp-40'] && rec['exp-40'] !== 0) volume += rec['exp-40'] * 2;
    if (rec['exp-45'] && rec['exp-45'] !== 0) volume += rec['exp-45'] * 2;
    if (rec['imp-20'] && rec['imp-20'] !== 0) volume += rec['imp-20'];
    if (rec['imp-40'] && rec['imp-40'] !== 0) volume += rec['imp-40'] * 2;
    if (rec['imp-45'] && rec['imp-45'] !== 0) volume += rec['imp-45'] * 2;
    return volume;
  }
}


class CBMPieChart extends PieChartTemplateModel {
  label = 'CBM';

  calculateVolume = (rec: any) => {
    let volume = 0;
    if (rec['status'] !== 'Won') return volume;
    if (rec['exp-lcl-cbm'] && rec['exp-lcl-cbm'] !== 0) volume += rec['exp-lcl-cbm'];
    if (rec['imp-lcl-cbm'] && rec['imp-lcl-cbm'] !== 0) volume += rec['imp-lcl-cbm'];
    return volume;
  }
}


class KGPieChart extends PieChartTemplateModel {
  label = 'KG';

  calculateVolume = (rec: any) => {
    let volume = 0;
    if (rec['status'] !== 'Won') return volume;
    if (rec['exp-air-kgs'] && rec['exp-air-kgs'] !== 0) volume += rec['exp-air-kgs'];
    if (rec['imp-air-kgs'] && rec['imp-air-kgs'] !== 0) volume += rec['imp-air-kgs'];
    return volume;
  }
}


class ShipmentPieChart extends PieChartTemplateModel {
  label = 'SHIPMENT';

  calculateVolume = (rec: any) => {
    let volume = 0;
    if (rec['status'] !== 'Won') return volume;
    if (rec['exp-breakbulk-shmt'] && rec['exp-breakbulk-shmt'] !== 0) volume += rec['exp-breakbulk-shmt'];
    if (rec['breakbulk-shpt-i'] && rec['breakbulk-shpt-i'] !== 0) volume += rec['breakbulk-shpt-i'];
    return volume;
  }
}

interface PieChartTemplateProps extends bs.BaseComponentProps {
  model: chart.PieChartTemplateModel;
  forceSmallScreen?: boolean;
}
export class PieChartTemplate extends bs.AvailableSize<PieChartTemplateProps> {
  data: Array<any>;
  constructor(props: PieChartTemplateProps) {
    super(props);
    this.data = props.model.computeData();
  }

  renderCells() {
    let holder = new Array<React.ReactElement>();
    this.data.map((_entry, index) => {
      holder.push(
        <Cell key={`cell-${index}`} fill={util.Colors.randomColor()} />
      );
    });
    return holder;
  }

  renderContent(width: number, _height: number) {
    let { model, forceSmallScreen } = this.props;
    let config = model.config;
    let dataKey: string = model.config.dataKey;
    if (!dataKey) dataKey = "value";
    let isSmallScreen = false;
    if (forceSmallScreen) {
      isSmallScreen = forceSmallScreen;
    } else {
      isSmallScreen = bs.ScreenUtil.isSmallScreen();
    }

    return (
      <div>
        <PieChart width={width} height={config.height ? config.height : 600}
          margin={{ top: 10, right: 30, bottom: 60, left: 30 }}>
          <Pie dataKey={dataKey} isAnimationActive={true} data={this.data}
            labelLine={config.labelLine}
            label={config.customLabel ? model.renderCustomizedLabel : true}
            cx="50%" cy="50%"
            outerRadius={config.radius} fill="#8884d8" values="1500"
            minAngle={5}>
            {this.renderCells()}
          </Pie>
          <Tooltip formatter={model.formatTooltip} />
          <Legend verticalAlign="bottom"
            layout="vertical"
            align="center"
            content={config.customLegend ? model.renderLegendContent : undefined} />
        </PieChart>
      </div >
    );
  }
}

interface VolumeRatioProps extends app.AppComponentProps {
  records: Array<any>;
  model: chart.PieChartTemplateModel<any>;
}

export class VolumeRatio extends app.AppComponent<VolumeRatioProps> {
  render(): React.ReactNode {
    let { appContext, records, model } = this.props;
    if (!records) records = [];
    if (records.length == 0 || !records) {
      appContext.addOSNotification("danger", T("No Records were found"));
      return;
    } else {

      return (
        <PieChartTemplate model={model} />
      )
    }
  }
}
interface UIReportIndividualVolumeProps extends app.AppComponentProps {
  records: Array<any>;
}
export class UIReportIndividualVolume extends app.AppComponent<UIReportIndividualVolumeProps> {
  render() {
    let { appContext, pageContext, records } = this.props;
    const CONFIG: chart.PieChartTemplateConfig = {
      radius: 100,
      labelLine: true,
      customLabel: false,
      customLegend: true,
      height: 400,
    }
    return (
      <div className="flex-hbox">
        <div style={{ width: 500 }}>
          <bs.Card header={T("TEU")} key={'teu-ratio'}>
            <VolumeRatio appContext={appContext} pageContext={pageContext} records={records} model={new TEUPieChart(CONFIG, records)} />
          </bs.Card>
        </div>
        <div style={{ width: 500 }}>
          <bs.Card header={T("CBM")} key={'cbm-ratio'}>
            <VolumeRatio appContext={appContext} pageContext={pageContext} records={records} model={new CBMPieChart(CONFIG, records)} />
          </bs.Card>
        </div>
        <div style={{ width: 500 }}>
          <bs.Card header={T("KG")} key={'kg-ratio'}>
            <VolumeRatio appContext={appContext} pageContext={pageContext} records={records} model={new KGPieChart(CONFIG, records)} />
          </bs.Card>
        </div>
        <div style={{ width: 500 }}>
          <bs.Card header={T("SHIPMENT (BREAKBULK)")} key={'shipment-ratio'}>
            <VolumeRatio appContext={appContext} pageContext={pageContext} records={records} model={new ShipmentPieChart(CONFIG, records)} />
          </bs.Card>
        </div>
      </div>
    );
  }
}