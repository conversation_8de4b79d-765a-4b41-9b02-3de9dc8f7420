import React, { ReactElement } from "react";
import * as FeatherIcon from 'react-feather';
import { app, bs, grid, entity, util, input, chart } from "@datatp-ui/lib";

import { T } from "../../Dependency";

const SESSION = app.host.DATATP_SESSION;

class Pie<PERSON>hart extends chart.PieChartTemplateModel<any> {
  constructor(config: chart.PieChartTemplateConfig, records: Array<any>) {
    super(config, records);
  }

  override renderLegendContent = (pieProps: any): ReactElement => {
    const { payload } = pieProps;
    let total: number = 0;
    let html = (
      <div style={{ fontSize: 10, overflow: "scroll", maxHeight: 350, width: 500 }}>
        <table className='table table-sm table-hover'>
          <thead>
            <tr>
              <th scope="col">SALEMAN</th>
              <th scope="col">INQUIRIES</th>
              <th scope="col">PERCENT</th>
            </tr>
          </thead>
          <tbody>
            {payload.map((entry: any, index: any) => {
              total += entry.payload.value
              return <tr style={{ color: entry.color }}>
                <td scope="row" className='flex-hbox'>
                  <div className="fw-bold" > {entry.payload.name} </div>
                </td>
                <td scope="row" className='text-right'> {entry.payload.value} </td>
                <td scope="row"> {`${(entry.payload.percent * 100).toFixed(0)}%`} </td>
              </tr>
            })}
            <tr>
              <td scope="row" className='flex-hbox'>
                <div className="fw-bold text-uppercase" >TOTAL</div>
              </td>
              <td scope="row" className='text-right'>{total}</td>
              <td scope="row"></td>
            </tr>
          </tbody>
        </table>
      </div>
    )
    return html;
  }

  override computeData() {
    let recordMap: Map<string, number> = new Map<string, number>();
    for (let record of this.records) {
      if (!recordMap.get(record['_owner_'])) {
        recordMap.set(record['_owner_'], 1);
      } else {
        let total = recordMap.get(record['_owner_'])!;
        total++;
        recordMap.set(record['_owner_'], total);
      }
    }
    let records = Array.from(recordMap, ([key, value]) => {
      return { 'name': key.toUpperCase(), 'value': value };
    });
    console.log(records);

    return records;
  }

  override renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }: any): any => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);
    return (
      <text
        x={x} y={y} textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central"
        className='fw-bold' fill='white' style={{ fontSize: 10 }}>
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };
}

interface InquiryRatioProps extends app.AppComponentProps {
  records?: Array<any>;
}

export class InquiryRatio extends app.AppComponent<InquiryRatioProps> {
  render(): React.ReactNode {
    let { appContext, records } = this.props;
    if (!records) records = [];
    if (records.length == 0 || !records) {
      appContext.addOSNotification("danger", T("No Records were found"));
      return;
    } else {
      const CONFIG: chart.PieChartTemplateConfig = {
        radius: 150,
        labelLine: false,
        customLabel: true,
        customLegend: true,
        height: 400,
      }
      const MODEL: PieChart = new PieChart(CONFIG, records);
      return (
        <chart.PieChartTemplate model={MODEL} />
      )
    }
  }
}