import React from "react";
import { app, bs, util, chart } from "@datatp-ui/lib";

import { T } from "../../Dependency";

interface DataTemplateProps extends app.AppComponentProps {
  records: Array<any>;
}

class DataTemplate extends app.AppComponent<DataTemplateProps> {
  render(): React.ReactNode {
    const { records } = this.props;
    let config: bs.GridConfig = {
      showHeader: true,
      showBorder: true,
      columns: [
        { label: T("Month"), field: "month", width: 150, },
        { label: T('Inquiry'), field: 'inquiry-count', width: 100, cssClass: 'justify-content-end' },
        { label: T('Booking'), field: 'booking-count', width: 100, cssClass: 'justify-content-end' },
        { label: T('Agent Won'), field: 'agent-won', width: 100, cssClass: 'justify-content-end' },
      ],
    }
    return (
      <bs.Grid config={config} beans={records} />
    )
  }
}

interface UIReportTotalShipmentWonProps extends bs.BaseComponentProps {
  appContext: app.AppContext;
  pageContext: app.PageContext;
  records: Array<any>;
}

export class UIReportTotalShipmentWon extends bs.AvailableSize<UIReportTotalShipmentWonProps> {

  override renderContent(width: number, height: number): any {
    let { appContext, pageContext, records, style } = this.props;

    records = [...records].sort((a, b) => {
      const dateA = util.TimeUtil.parseCompactDateTimeFormat(a['date'] || '');
      const dateB = util.TimeUtil.parseCompactDateTimeFormat(b['date'] || '');
      return dateA.getTime() - dateB.getTime(); // Ascending order
    });

    let recordComposeCharts: any = [];
    let recordMapByMonth: Map<string, any[]> = new Map<string, any[]>();

    for (let record of records) {
      let date = util.TimeUtil.parseCompactDateTimeFormat(record['date']);
      let month = util.TimeUtil.toMonthIdFormat(date);

      if (!recordMapByMonth.get(month)) {
        recordMapByMonth.set(month, [record]);
      } else {
        let recs: any = recordMapByMonth.get(month);
        recs.push(record);
        recordMapByMonth.set(month, recs);
      }
    }

    for (let month of recordMapByMonth.keys()) {
      let bookingCount = 0;
      let agentWon = 0;

      let recs: any = recordMapByMonth.get(month);
      for (let rec of recs) {
        if (!rec['booking-qty'] || rec['booking-qty'] == 0) continue;
        bookingCount += rec['booking-qty'];
        agentWon++;
      }

      let composeChartRecord = {
        'month': [month.slice(0, 2), '/', month.slice(2)].join(''),
        'inquiry-count': recs.length,
        'booking-count': bookingCount,
        'agent-won': agentWon,
      }
      recordComposeCharts.push(composeChartRecord);
    }

    const WARNING_HEX_COLOR = "#DE780B";
    const SECONDARY_HEX_COLOR = "#9FA6B2";
    const SUCCESS_HEX_COLOR = "#14A44D";
    let config: chart.ComposedChartTemplateConfig = {
      xAxis: { label: 'TIME', dataKey: 'month' },
      leftYAxis: { label: 'QUANTITY' },
      lines: [
        { yAxisId: 'left', name: 'Inquiry', dataKey: 'inquiry-count', strokeWidth: 3, color: SECONDARY_HEX_COLOR, visible: true, },
        { yAxisId: 'left', name: 'Booking', dataKey: 'booking-count', strokeWidth: 3, color: SUCCESS_HEX_COLOR, visible: true },
        { yAxisId: 'left', name: 'Agent Won', dataKey: 'agent-won', strokeWidth: 3, color: WARNING_HEX_COLOR, visible: true },
      ],
      stackedBarChart: false,
    };
    const CHART_TEMPLATE_MODEL = new chart.ComposedChartTemplateModel(config, recordComposeCharts);

    return (
      <div className="flex-hbox" style={style}>
        <div className="p-2" style={{ width: 500 }}>
          <DataTemplate appContext={appContext} pageContext={pageContext} records={recordComposeCharts} />
        </div>
        <div className="flex-vbox flex-grow-1">
          <chart.ComposedChartTemplate model={CHART_TEMPLATE_MODEL} height={550} />
        </div>
      </div>
    );
  }
}
