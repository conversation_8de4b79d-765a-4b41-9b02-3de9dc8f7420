import React from "react";
import * as FeatherIcon from 'react-feather';
import { app, bs, grid, entity, util, input } from "@datatp-ui/lib";

import { T } from "../../Dependency";
import { UIRowList } from "../../data/UIRowList";
import { UIReportIndividualVolume } from "./ReportIndividualVolume";
import { UIReportIndividualQuotation } from "./ReportIndividualQuotation";
import { InquiryRatio } from "./ReportInquiryRatio";
import { UIReportTotalShipmentWon } from "./ReportShipmentWon";

const SESSION = app.host.DATATP_SESSION;

interface UIBDQuotationDailyReportProps extends app.AppComponentProps {
  reportRecords: Array<any>
  records: Array<any>
  reportMonth: any;
}

class UIBDQuotationDailyReport extends app.AppComponent<UIBDQuotationDailyReportProps> {
  render() {
    let { appContext, pageContext, records, reportRecords, reportMonth } = this.props;
    let monthStr = util.TimeUtil.toMonthIdFormat(reportMonth);
    let monthString = [monthStr.slice(0, 2), '/', monthStr.slice(2)].join('')
    let toDayStr = util.TimeUtil.toCompactDateFormat(new Date());
    return (
      <div className="flex-vbox">
        <div className="flex-grow-0 p-2 border-bottom">
          <div className="fw-bold">{T("IST QUOTATION REPORT - " + monthString)}</div>
          <div className="flex-hbox fw-bold">
            <div className="me-5">Date: {toDayStr}</div>
            <div className="ms-5">By: {app.host.DATATP_SESSION.getAccountAcl().getFullName().toUpperCase()} - {app.host.DATATP_SESSION.getAccountAcl().getLoginId().toUpperCase()}</div>
          </div>
        </div>
        <bs.GreedyScrollable className="flex-vbox">
          <UIReportIndividualQuotation appContext={appContext} pageContext={pageContext}
            records={records} reportRecords={reportRecords} />
          <bs.Card header={T("Volume")} key={'volume-ratio'}>
            <UIReportIndividualVolume appContext={appContext} pageContext={pageContext} records={reportRecords} />
          </bs.Card>
          <bs.Card header={T("Inquiry Ratio")} key={'ist-inquiry-ratio'}>
            <InquiryRatio appContext={appContext} pageContext={pageContext} records={records} />
          </bs.Card>
          <bs.Card header="Chart">
            <UIReportTotalShipmentWon appContext={appContext} pageContext={pageContext} records={records} />
          </bs.Card>
        </bs.GreedyScrollable>
      </div>
    )
  }
}

const BDISTDailyReport = (context: grid.VGridContext) => {
  if (!['vince.vnsgn', 'nhat.le'].includes(SESSION.getLoginId())) {
    let message = "You don't have permission";
    bs.notificationShow("danger", message, <div className='alert alert-danger'>{message}</div>);
    return;
  }
  let uiRoot = context.uiRoot as UIRowList;
  let { appContext, pageContext, plugin } = uiRoot.props;

  let onReport = (reportModel: any) => {
    let records = plugin.getListModel().getRecords() ?? [];
    let reportMonth = util.TimeUtil.parseCompactDateTimeFormat(reportModel['month']);
    let reportRecords: any = [];
    for (let record of records) {
      if (!record['date']) continue;
      let date = util.TimeUtil.parseCompactDateTimeFormat(record['date']);
      if (date.getMonth() == reportMonth.getMonth() && date.getFullYear() == reportMonth.getFullYear()) {
        reportRecords.push(record)
      }
    }

    let createUI = () => (
      <UIBDQuotationDailyReport appContext={appContext} pageContext={pageContext}
        records={records} reportRecords={reportRecords} reportMonth={reportMonth} />
    );
    context.getVGrid().addTab("QUOTATION TASK REPORT", createUI);
    context.getVGrid().forceUpdateView();
  }

  let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
    let reportModel = { month: util.TimeUtil.javaCompactDateTimeFormat(new Date()) };
    return (
      <div className="flex-vbox">
        <div className="flex-vbox">
          <input.BBDateTimeField
            label={T('Month')}
            bean={reportModel} field={'month'} dateFormat={'MM/YYYY'} timeFormat={false} />
        </div>
        <bs.Toolbar>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext} color='secondary'
            label={T('Report')} icon={FeatherIcon.Book} onClick={() => {
              onReport(reportModel)
              pageCtx.back();
            }} />
        </bs.Toolbar>
      </div>
    );
  }
  pageContext.createPopupPage('select-month', 'Select Month', createPageContent)
}

export const BDISTDailyReportAction: grid.VGridActionConfig = {
  name: 'bd-ist-daily-report',
  label: 'Report',
  icon: FeatherIcon.FileText,
  onClick: BDISTDailyReport
}