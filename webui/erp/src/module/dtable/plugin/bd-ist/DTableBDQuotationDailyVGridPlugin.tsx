import { grid } from '@datatp-ui/lib';
import { DTableVGridPlugin } from "./DTableVGridPlugin";
import { initVgridConfigBDQuotationConfigPlugin } from './DTableBDQuotationDailyConfigPlugin';
import { BDQuotationDailyReportAction } from './DTableBDQuotationDailyReportPlugin';

export const DTableBDQuotationDailyVGridPlugin: DTableVGridPlugin = {
  name: 'quotation-daily-report-agency-sales-vgrid-plugin', label: 'Actions',
  isApplyFor(ctx: grid.VGridContext) {
    return true;
  },
  toolbar: {
    actions: [
      BDQuotationDailyReportAction
    ],
    dropdownActions: [
    ]
  },
  onInitVGrid(ctx: grid.VGridContext) {
    initVgridConfigBDQuotationConfigPlugin(ctx);
  },
}