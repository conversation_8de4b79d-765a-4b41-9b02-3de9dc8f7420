import React from "react";
import * as FeatherIcon from 'react-feather';
import { app, bs, grid, entity, util, input, chart } from "@datatp-ui/lib";

import { T } from "../../Dependency";
import { UIRowList } from "../../data/UIRowList";

const SESSION = app.host.DATATP_SESSION;

class UIReportByMonthList extends entity.DbEntityList {
  createVGridConfig() {
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 150,
        fields: [
          {
            label: T("NO."), name: "stt", width: 30, container: 'fixed-left',
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T('SALESMAN'), name: 'saleman', width: 100, container: 'fixed-left',
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T('REGION'), name: 'regional-division', width: 120,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          { label: T('NO. OF INQUIRY'), name: 'inquiry-count', width: 160 },
          { label: T("NO. OF WON SHIPMENT"), name: 'shipment-count', width: 210 },
          { label: T("% WIN"), name: 'win-rate', width: 80 },
          {
            label: T("AGENT (WON)"), name: 'agent-won', width: 400,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T("COUNTRY"), name: 'country', width: 150,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T("NETWORK"), name: 'network', width: 120,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T("SOURCE"), name: 'source', width: 150,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
          {
            label: T("SERVICE"), name: 'service', width: 400,
            customRender: (ctx, field, record, focus) => {
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record.record} field={field.name} style={{ height: height }} />
              );
            }
          },
        ],
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_EXPORT_XLSX()
        ]
      },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }
}

class ProportionOfMarketList extends entity.DbEntityList {
  createVGridConfig() {
    let config: grid.VGridConfig = {
      record: {
        fields: [
          { label: T("MARKET"), name: "market", width: 500, container: 'fixed-left', },
          { label: T('PROPORTION (%)'), name: 'proportion', width: 200, cssClass: 'justify-content-end', container: 'fixed-left', },
        ],
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_EXPORT_XLSX()
        ]
      },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }
}

interface DataTemplateProps extends app.AppComponentProps {
  records: Array<any>;
}

class DataTemplate extends app.AppComponent<DataTemplateProps> {
  render(): React.ReactNode {
    const { records } = this.props;
    let config: bs.GridConfig = {
      showHeader: true,
      showBorder: true,
      columns: [
        { label: T("Month"), field: "month", width: 150, },
        { label: T('Inquiry'), field: 'inquiry-count', width: 100, cssClass: 'justify-content-end' },
        { label: T('Booking'), field: 'booking-count', width: 100, cssClass: 'justify-content-end' },
        { label: T('Agent Won'), field: 'agent-won', width: 100, cssClass: 'justify-content-end' },
      ],
    }
    return (
      <bs.Grid config={config} beans={records} />
    )
  }
}

interface UIComposedChartProps extends bs.BaseComponentProps {
  appContext: app.AppContext;
  pageContext: app.PageContext;
  records: Array<any>;
}

class UIComposedChart extends bs.AvailableSize<UIComposedChartProps> {

  override renderContent(width: number, height: number): any {
    let { appContext, pageContext, records, style } = this.props;

    records = [...records].sort((a, b) => {
      const dateA = util.TimeUtil.parseCompactDateTimeFormat(a['date'] || '');
      const dateB = util.TimeUtil.parseCompactDateTimeFormat(b['date'] || '');
      return dateA.getTime() - dateB.getTime(); // Ascending order
    });

    let recordComposeCharts: any = [];
    let recordMapByMonth: Map<string, any[]> = new Map<string, any[]>();

    for (let record of records) {
      let date = util.TimeUtil.parseCompactDateTimeFormat(record['date']);
      let month = util.TimeUtil.toMonthIdFormat(date);

      if (!recordMapByMonth.get(month)) {
        recordMapByMonth.set(month, [record]);
      } else {
        let recs: any = recordMapByMonth.get(month);
        recs.push(record);
        recordMapByMonth.set(month, recs);
      }
    }

    for (let month of recordMapByMonth.keys()) {
      let bookingCount = 0;
      let agentWon = 0;

      let recs: any = recordMapByMonth.get(month);
      for (let rec of recs) {
        if (!rec['booking-qty'] || rec['booking-qty'] == 0) continue;
        bookingCount += rec['booking-qty'];
        agentWon++;
      }

      let composeChartRecord = {
        'month': [month.slice(0, 2), '/', month.slice(2)].join(''),
        'inquiry-count': recs.length,
        'booking-count': bookingCount,
        'agent-won': agentWon,
      }
      recordComposeCharts.push(composeChartRecord);
    }

    const WARNING_HEX_COLOR = "#DE780B";
    const SECONDARY_HEX_COLOR = "#9FA6B2";
    const SUCCESS_HEX_COLOR = "#14A44D";
    let config: chart.ComposedChartTemplateConfig = {
      xAxis: { label: 'TIME', dataKey: 'month' },
      leftYAxis: { label: 'QUANTITY' },
      lines: [
        { yAxisId: 'left', name: 'Inquiry', dataKey: 'inquiry-count', strokeWidth: 3, color: SECONDARY_HEX_COLOR, visible: true, },
        { yAxisId: 'left', name: 'Booking', dataKey: 'booking-count', strokeWidth: 3, color: SUCCESS_HEX_COLOR, visible: true },
        { yAxisId: 'left', name: 'Agent Won', dataKey: 'agent-won', strokeWidth: 3, color: WARNING_HEX_COLOR, visible: true },
      ],
      stackedBarChart: false,
    };
    const CHART_TEMPLATE_MODEL = new chart.ComposedChartTemplateModel(config, recordComposeCharts);

    return (
      <div className="flex-hbox" style={style}>
        <div className="p-2" style={{ width: 500 }}>
          <DataTemplate appContext={appContext} pageContext={pageContext} records={recordComposeCharts} />
        </div>
        <div className="flex-vbox flex-grow-1">
          <chart.ComposedChartTemplate model={CHART_TEMPLATE_MODEL} height={550} />
        </div>
      </div>
    );
  }
}

interface QuotationTaskReportProps extends app.AppComponentProps {
  reportRecords: Array<any>
  records: Array<any>
}

class QuotationTaskReport extends app.AppComponent<QuotationTaskReportProps> {
  collectVolume = (rec: any) => {
    let volume = 'N/A';
    if (rec['exp-20'] && rec['exp-20'] !== 0) volume = rec['exp-20'] + "x20'";
    if (rec['exp-40'] && rec['exp-40'] !== 0) volume = rec['exp-40'] + "x40'";
    if (rec['exp-lcl-cbm'] && rec['exp-lcl-cbm'] !== 0) volume = rec['exp-lcl-cbm'] + " (CBM)";
    if (rec['exp-air-kgs'] && rec['exp-air-kgs'] !== 0) volume = rec['exp-air-kgs'] + " (KGS)'";
    if (rec['exp-breakbulk-shmt'] && rec['exp-breakbulk-shmt'] !== 0) volume = rec['exp-breakbulk-shmt'] + " (Shpt)";
    if (rec['imp-20'] && rec['imp-20'] !== 0) volume = rec['imp-20'] + "x20'";
    if (rec['imp-40'] && rec['imp-40'] !== 0) volume = rec['imp-40'] + "x40'";
    if (rec['imp-lcl-cbm'] && rec['imp-lcl-cbm'] !== 0) volume = rec['imp-lcl-cbm'] + " (CBM)";
    if (rec['imp-air-kgs'] && rec['imp-air-kgs'] !== 0) volume = rec['imp-air-kgs'] + " (KGS)'";
    if (rec['breakbulk-shpt-i'] && rec['breakbulk-shpt-i'] !== 0) volume = rec['breakbulk-shpt-i'] + " (Shpt)";
    return volume;
  }

  render() {
    let { appContext, pageContext, records, reportRecords } = this.props;
    let recordMapBySaleman: Map<string, Array<any>> = new Map<string, Array<any>>();

    for (let record of reportRecords) {
      let saleman = record['_owner_'];
      if (!saleman || saleman.length < 1) continue;

      if (!recordMapBySaleman.get(saleman)) {
        recordMapBySaleman.set(saleman, [record]);
      } else {
        let recs = recordMapBySaleman.get(saleman)!;
        recs.push(record);
        recordMapBySaleman.set(saleman, recs);
      }
    }

    recordMapBySaleman = new Map([...recordMapBySaleman.entries()].sort())

    let reportByMonthRecords: any = [];
    let proportionOfMarketRecords: any = [];
    let proportionAmericaEU = 0;
    let proportionSNAsiaAfricaOceania = 0;
    let proportionWAsiaMECAsia = 0;
    let proportionEAsiaSea = 0;
    let stt = 1;

    for (let saleman of recordMapBySaleman.keys()) {
      let regionalDivisions = '';
      let inquiryCount = 0;
      let shipmentCount = 0;
      let winRate = 0;
      let agentWon = '';
      let country = '';
      let networks = '';
      let sources = '';
      let service = '';
      let recs: any = recordMapBySaleman.get(saleman);

      for (let rec of recs) {
        if (!rec['booking-qty'] || rec['booking-qty'] == 0) continue;
        shipmentCount += rec['booking-qty'] ? rec['booking-qty'] : 0;
        agentWon += rec['agent-name'] ? "- " + rec['agent-name'] + '\r\n' : '- N/A \r\n';
        country += rec['country'] ? "- " + rec['country'] + '\r\n' : '- N/A \r\n';
        networks += rec['source'] ? "- " + rec['source'] + '\r\n' : '- N/A \r\n';
        sources += rec['network'] ? "- " + rec['network'] + '\r\n' : '- N/A \r\n';
        let volume = this.collectVolume(rec);
        let incoterms = rec['incoterms'] ? rec['incoterms'] : 'N/A \r\n';
        let routes = rec['route'] ? rec['route'] : 'N/A \r\n';
        service += "- " + volume + " & " + incoterms + " & " + routes + '\r\n';
      }

      inquiryCount = recs.length;
      winRate = (shipmentCount / inquiryCount);

      if (['vince.vnsgn', 'Maria.vnsgn', 'coey.vnsgn'].includes(saleman)) {
        regionalDivisions = 'America + EU';
        proportionAmericaEU += recs.length;
      } else if (['joye.vnsgn'].includes(saleman)) {
        regionalDivisions = 'South/North Asia + Africa + Oceania';
        proportionSNAsiaAfricaOceania += recs.length;
      } else if (['tess.vnsgn'].includes(saleman)) {
        regionalDivisions = 'West Asia/M.E + Central Asia';
        proportionWAsiaMECAsia += recs.length;
      } else if (['MASON.VNHPH'].includes(saleman)) {
        regionalDivisions = 'East Asia + S.E.A';
        proportionEAsiaSea += recs.length;
      }

      reportByMonthRecords.push({
        'stt': stt++,
        'saleman': saleman,
        'regional-division': regionalDivisions,
        'inquiry-count': inquiryCount,
        'shipment-count': shipmentCount,
        'win-rate': util.text.formater.percent(winRate),
        'agent-won': agentWon.length > 0 ? agentWon : '- N/A',
        'country': country.length > 0 ? country : '- N/A',
        'network': networks.length > 0 ? networks : '- N/A',
        'source': sources.length > 0 ? sources : '- N/A',
        'service': service.length > 0 ? service : '- N/A',
      })
    }
    let proportionAmericaEUPercent = util.text.formater.percent((proportionAmericaEU / reportRecords.length));
    let proportionSNAsiaAfricaOceaniaPercent = util.text.formater.percent((proportionSNAsiaAfricaOceania / reportRecords.length));
    let proportionWAsiaMECAsiaPercent = util.text.formater.percent((proportionWAsiaMECAsia / reportRecords.length));
    let proportionEAsiaSeaPercent = util.text.formater.percent((proportionEAsiaSea / reportRecords.length));

    proportionOfMarketRecords.push({ market: 'America + EU', proportion: proportionAmericaEUPercent });
    proportionOfMarketRecords.push({ market: 'South/North Asia + Africa + Oceania', proportion: proportionSNAsiaAfricaOceaniaPercent })
    proportionOfMarketRecords.push({ market: 'West Asia/M.E + Central Asia', proportion: proportionWAsiaMECAsiaPercent });
    proportionOfMarketRecords.push({ market: 'East Asia + S.E.A', proportion: proportionEAsiaSeaPercent });

    return (
      <div className="flex-vbox">
        <UIReportByMonthList key={`report-by-month`} appContext={appContext} pageContext={pageContext}
          plugin={new entity.DbEntityListPlugin(reportByMonthRecords)} style={{ minHeight: 800 }} />
        <ProportionOfMarketList key={'proportion-of-market'} appContext={appContext} pageContext={pageContext}
          plugin={new entity.DbEntityListPlugin(proportionOfMarketRecords)} style={{ minHeight: 230 }} />
      </div>
    )
  }
}

interface UIBDQuotationDailyReportProps extends app.AppComponentProps {
  reportRecords: Array<any>
  records: Array<any>
  reportMonth: any;
}

class UIBDQuotationDailyReport extends app.AppComponent<UIBDQuotationDailyReportProps> {
  render() {
    let { appContext, pageContext, records, reportRecords, reportMonth } = this.props;
    let monthStr = util.TimeUtil.toMonthIdFormat(reportMonth);
    let monthString = [monthStr.slice(0, 2), '/', monthStr.slice(2)].join('')
    let toDayStr = util.TimeUtil.toCompactDateFormat(new Date());
    return (
      <div className="flex-vbox">
        <div className="flex-grow-0 p-2 border-bottom">
          <div className="fw-bold">{T("OVERSEAS DEVELOPMENT DEPARTMENT QUOTATION REPORT - " + monthString)}</div>
          <div className="flex-hbox fw-bold">
            <div className="me-5">Date: {toDayStr}</div>
            <div className="ms-5">By: {app.host.DATATP_SESSION.getAccountAcl().getFullName().toUpperCase()} - {app.host.DATATP_SESSION.getAccountAcl().getLoginId().toUpperCase()}</div>
          </div>
        </div>
        <bs.GreedyScrollable className="flex-vbox">
          <QuotationTaskReport appContext={appContext} pageContext={pageContext}
            records={records} reportRecords={reportRecords} />
          <bs.Card header="Chart">
            <UIComposedChart appContext={appContext} pageContext={pageContext} records={records} />
          </bs.Card>
        </bs.GreedyScrollable>
      </div>
    )
  }
}

const BDQuotationDailyReport = (context: grid.VGridContext) => {
  if (!['vince.vnsgn', 'nhat.le'].includes(SESSION.getLoginId())) {
    let message = "You don't have permission";
    bs.notificationShow("danger", message, <div className='alert alert-danger'>{message}</div>);
    return;
  }
  let uiRoot = context.uiRoot as UIRowList;
  let { appContext, pageContext, plugin } = uiRoot.props;

  let onReport = (reportModel: any) => {
    let records = plugin.getListModel().getRecords() ?? [];
    let reportMonth = util.TimeUtil.parseCompactDateTimeFormat(reportModel['month']);
    let reportRecords: any = [];
    for (let record of records) {
      if (!record['date']) continue;
      let date = util.TimeUtil.parseCompactDateTimeFormat(record['date']);
      if (date.getMonth() == reportMonth.getMonth() && date.getFullYear() == reportMonth.getFullYear()) {
        reportRecords.push(record)
      }
    }

    let ui = () => <UIBDQuotationDailyReport appContext={appContext} pageContext={pageContext}
      records={records} reportRecords={reportRecords} reportMonth={reportMonth} />
    context.getVGrid().addTab("QUOTATION TASK REPORT", ui);
    context.getVGrid().forceUpdateView();
  }

  let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
    let reportModel = { month: util.TimeUtil.javaCompactDateTimeFormat(new Date()) };
    return (
      <div className="flex-vbox">
        <div className="flex-vbox">
          <input.BBDateTimeField
            label={T('Month')}
            bean={reportModel} field={'month'} dateFormat={'MM/YYYY'} timeFormat={false} />
        </div>
        <bs.Toolbar>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext} color='secondary'
            label={T('Report')} icon={FeatherIcon.Book} onClick={() => {
              onReport(reportModel)
              pageCtx.back();
            }} />
        </bs.Toolbar>
      </div>
    );
  }
  pageContext.createPopupPage('select-month', 'Select Month', createPageContent)
}

export const BDQuotationDailyReportAction: grid.VGridActionConfig = {
  name: 'bd-quotation-daily-report',
  label: 'Report',
  icon: FeatherIcon.FileText,
  onClick: BDQuotationDailyReport
}