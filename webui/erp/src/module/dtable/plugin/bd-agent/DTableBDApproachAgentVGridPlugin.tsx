import { grid } from '@datatp-ui/lib';

import { DTableVGridPlugin } from "../DTableVGridPlugin";
import { initVgridConfigBulkCargo } from '../DTableBulkCargoConfigPlugin';
import { ReportPlugin } from './DTableBDApproachAgentSaleReport';

export const DTableBDApproachAgentVGridPlugin: DTableVGridPlugin = {
  name: 'bang-theo-doi-approach-agent-customer-bd-vgrid-plugin', label: 'Actions',
  isApplyFor(ctx: grid.VGridContext) {
    return true;
  },
  toolbar: {
    actions: [
      ReportPlugin
    ],
    dropdownActions: [
    ]
  },
  onInitVGrid(ctx: grid.VGridContext) {
    initVgridConfigBulkCargo(ctx);
  },
}